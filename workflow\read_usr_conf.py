#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import os
import toml
import random


def read_usr_conf(usr_conf_file, logger):
    """
    Read user configuration and validate field types according to new config structure
    根据最新配置结构读取用户配置文件并验证字段类型
    """
    if not usr_conf_file or not os.path.exists(usr_conf_file):
        logger.error(f"文件不存在: {usr_conf_file}, 请检查")
        return None

    try:
        with open(usr_conf_file, "r", encoding="utf-8") as file:
            loaded_usr_conf = toml.load(file)
    except Exception as e:
        logger.error(f"无法加载TOML文件: {usr_conf_file}. 错误: {e}, 请检查")
        return None

    # 更新字段类型映射（按section分层）
    valid_key_types = {
        "monitor": {"monitor_side": [int], "auto_switch_monitor_side": [bool]},
        "episode": {"opponent_agent": [str], "eval_interval": [int], "eval_opponent_type": [str, list]},
        "lineups": {"blue_camp": [list], "red_camp": [list]},
    }

    # 新增必需字段检查（按section分层）
    required_sections = {
        "monitor": ["monitor_side", "auto_switch_monitor_side"],
        "episode": ["opponent_agent", "eval_interval", "eval_opponent_type"],
        "lineups": ["blue_camp", "red_camp"],
    }

    # 统一化校验方法
    def validate_type(section, key, value, expected_type):
        actual_type = type(value)
        if actual_type not in expected_type:
            expected_names = [t.__name__ for t in expected_type]
            logger.error(f"[{section}.{key}] 类型不匹配! 应为: {expected_names}, 实际: {actual_type.__name__}")
            return False
        return True

    # 验证配置结构
    try:
        validated_conf = {}

        # 1. 检查必需section
        for section in required_sections:
            if section not in loaded_usr_conf:
                logger.error(f"缺失必需配置段: [{section}]")
                return None

        # 2. 分层验证字段
        for section, fields in valid_key_types.items():
            if section not in loaded_usr_conf:
                continue

            # 检查必需字段
            for req_field in required_sections[section]:
                if req_field not in loaded_usr_conf[section]:
                    logger.error(f"在[{section}]中缺失必需字段: {req_field}")
                    return None

                # lineups单独处理
                if section == "lineups":
                    if len(loaded_usr_conf[section][req_field]) != 1:
                        logger.error(f"在[{section}][{req_field}]中缺失阵容配置")
                        return None
                    for lineup in loaded_usr_conf[section][req_field]:
                        if "hero_id" not in lineup:
                            logger.error(f"在[{section}][{req_field}]中缺失必需字段: hero_id")
                            return None
                        actual_type = type(lineup["hero_id"])
                        if actual_type not in [int]:
                            logger.error(f"hero_id 类型不匹配! 应为: {int.__name__}, 实际: {actual_type.__name__}")
                            return None

            # 校验字段类型
            for field, expected_type in fields.items():
                if field not in loaded_usr_conf[section]:
                    continue

                if not validate_type(section, field, loaded_usr_conf[section][field], expected_type):
                    return None

        # 3. 特殊校验逻辑
        # 校验英雄ID范围 (128,169,176)

        # 校验阵营取值范围
        if loaded_usr_conf["monitor"]["monitor_side"] not in (0, 1):
            logger.error(f"[monitor.monitor_side] 非法值 {loaded_usr_conf['monitor']['monitor_side']}, 应为0或1")
            return None

        # 校验eval_interval取值范围
        eval_interval = loaded_usr_conf["episode"]["eval_interval"]
        if not isinstance(eval_interval, int) or eval_interval < 1:
            logger.error(f"[episode.eval_interval] 非法值 {eval_interval}, 应为大于等于1的整数")
            return None

        # 校验 opponent_agent 取值范围
        valid_opponent_agent = ["selfplay", "common_ai"]
        opponent_agent = loaded_usr_conf["episode"]["opponent_agent"]
        valid = True
        if opponent_agent in valid_opponent_agent:
            pass
        else:
            try:
                # 强制转换为整数（兼容 int/str/float 等类型）
                num = int(opponent_agent)
                valid = num > 0
            except (ValueError, TypeError):
                valid = False

        if not valid:
            logger.error(f"[episode.opponent_agent] 非法值 {opponent_agent}, 应为selfplay/common_ai/或自定义整数")
            return False

        # 校验eval_opponent_type取值范围 - 支持字符串或列表
        eval_opponent_type = loaded_usr_conf["episode"]["eval_opponent_type"]

        # 如果是列表，随机选择一个对手机
        if isinstance(eval_opponent_type, list):
            if len(eval_opponent_type) == 0:
                logger.error(f"[episode.eval_opponent_type] 列表不能为空")
                return False
            # 随机选择一个对手
            selected_opponent = random.choice(eval_opponent_type)
            logger.info(f"{eval_opponent_type} Random selected: {selected_opponent}")
            # 将随机选择的结果更新到配置中
            loaded_usr_conf["episode"]["eval_opponent_type"] = selected_opponent
            eval_opponent_type = selected_opponent

        # 验证单个对手值
        valid_opponent_types = ["selfplay", "common_ai"]
        valid = True
        if eval_opponent_type in valid_opponent_types:
            pass
        else:
            try:
                # 强制转换为整数（兼容 int/str/float 等类型）
                num = int(eval_opponent_type)
                valid = num > 0
            except (ValueError, TypeError):
                valid = False

        if not valid:
            logger.error(f"[episode.eval_opponent_type] 非法值 {eval_opponent_type}, 应为selfplay/common_ai/或自定义整数")
            return False

        # 合并已验证配置
        validated_conf.update({k: v for k, v in loaded_usr_conf.items() if k in valid_key_types})
        return validated_conf

    except Exception as e:
        logger.exception(f"配置校验异常: {str(e)}")
        return None