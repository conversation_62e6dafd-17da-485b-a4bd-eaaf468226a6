#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""

import torch
import torch.nn as nn
from torch.nn import ModuleDict
import torch.nn.functional as F

import numpy as np
from typing import List
from collections import OrderedDict

from agent_ppo.conf.conf import DimConfig, Config


def make_fc_layer(in_features: int, out_features: int, use_bias=True):
    """创建并初始化线性层的包装函数"""
    fc_layer = nn.Linear(in_features, out_features, bias=use_bias)
    nn.init.orthogonal_(fc_layer.weight)
    if use_bias:
        nn.init.zeros_(fc_layer.bias)
    return fc_layer


class MLP(nn.Module):
    def __init__(
        self,
        fc_feat_dim_list: List[int],
        name: str,
        non_linearity: nn.Module = nn.ReLU,
        non_linearity_last: bool = False,
    ):
        super(MLP, self).__init__()
        self.fc_layers = nn.Sequential()
        for i in range(len(fc_feat_dim_list) - 1):
            fc_layer = make_fc_layer(fc_feat_dim_list[i], fc_feat_dim_list[i + 1])
            self.fc_layers.add_module(f"{name}_fc{i + 1}", fc_layer)
            if i + 1 < len(fc_feat_dim_list) - 1 or non_linearity_last:
                self.fc_layers.add_module(f"{name}_non_linear{i + 1}", non_linearity())

    def forward(self, data):
        return self.fc_layers(data)


def masked_softmax(logits: torch.Tensor, legal_mask: torch.Tensor, dim: int = -1, eps: float = 1e-12) -> torch.Tensor:
    """
    稳定版 masked softmax:
    - 对非法动作将 logits 置为 -inf，从而 softmax 后概率为 0；
    - 若某样本全非法，则回退到 'no-op'（假设 no-op 在索引0），将其置为唯一合法动作；
    - 返回概率最小值下限裁剪到 eps，避免 log(0)。
    形状要求:
    - logits: [B, A]
    - legal_mask: [B, A]，元素为 {0,1}
    """
    assert logits.shape == legal_mask.shape, "logits 与 legal_mask 维度需一致"
    B, A = logits.shape
    mask_bool = legal_mask.bool()

    # 检查全非法的样本
    all_illegal = ~mask_bool.any(dim=1)  # [B]
    if all_illegal.any():
        # 将 no-op (index 0) 设为唯一合法
        mask_bool[all_illegal, :] = False
        mask_bool[all_illegal, 0] = True

    masked_logits = logits.masked_fill(~mask_bool, float('-inf'))
    probs = torch.softmax(masked_logits, dim=dim)

    # 数值安全下限
    probs = probs.clamp_min(eps)
    # 重新归一化（避免 clamp_min 后总和偏离1）
    probs = probs / probs.sum(dim=dim, keepdim=True).clamp_min(eps)
    return probs


def safe_log(x: torch.Tensor, eps: float = 1e-12) -> torch.Tensor:
    return torch.log(x.clamp_min(eps))


class Model(nn.Module):
    def __init__(self):
        super(Model, self).__init__()
        # feature configure parameter
        self.model_name = Config.NETWORK_NAME
        self.data_split_shape = Config.DATA_SPLIT_SHAPE
        self.lstm_time_steps = Config.LSTM_TIME_STEPS
        self.lstm_unit_size = Config.LSTM_UNIT_SIZE
        self.seri_vec_split_shape = Config.SERI_VEC_SPLIT_SHAPE
        self.m_learning_rate = Config.INIT_LEARNING_RATE_START
        self.m_var_beta = Config.BETA_START
        self.log_epsilon = Config.LOG_EPSILON
        self.label_size_list = Config.LABEL_SIZE_LIST
        self.is_reinforce_task_list = Config.IS_REINFORCE_TASK_LIST
        self.min_policy = Config.MIN_POLICY
        self.clip_param = Config.CLIP_PARAM
        self.restore_list = []
        self.var_beta = self.m_var_beta
        self.learning_rate = self.m_learning_rate
        self.target_embed_dim = Config.TARGET_EMBED_DIM
        self.cut_points = [value[0] for value in Config.data_shapes]
        self.legal_action_size = Config.LEGAL_ACTION_SIZE_LIST

        self.feature_dim = Config.SERI_VEC_SPLIT_SHAPE[0][0]
        self.legal_action_dim = int(np.sum(Config.LEGAL_ACTION_SIZE_LIST))
        self.lstm_hidden_dim = Config.LSTM_UNIT_SIZE

        # 维度配置
        self.hero_data_len = sum(Config.data_shapes[0])
        self.single_hero_feature_dim = int(DimConfig.DIM_OF_HERO_EMY[0])
        self.single_soldier_feature_dim = int(DimConfig.DIM_OF_SOLDIER_1_10[0])
        self.single_organ_feature_dim = int(DimConfig.DIM_OF_ORGAN_1_2[0])
        self.hero_main_feature_dim = int(DimConfig.DIM_OF_HERO_MAIN[0])
        self.global_feature_dim = int(DimConfig.DIM_OF_GLOBAL_INFO[0])
        self.all_hero_feature_dim = (
            int(np.sum(DimConfig.DIM_OF_HERO_FRD))
            + int(np.sum(DimConfig.DIM_OF_HERO_EMY))
            + int(np.sum(DimConfig.DIM_OF_HERO_MAIN))
        )
        self.all_soldier_feature_dim = int(np.sum(DimConfig.DIM_OF_SOLDIER_1_10)) + int(
            np.sum(DimConfig.DIM_OF_SOLDIER_11_20)
        )
        self.all_organ_feature_dim = int(np.sum(DimConfig.DIM_OF_ORGAN_1_2)) + int(np.sum(DimConfig.DIM_OF_ORGAN_3_4))

        
        fc_hero_main_dim_list = [self.hero_main_feature_dim, 64, 32, 16]
        self.hero_main_mlp = MLP(fc_hero_main_dim_list, "hero_main_mlp")
        fc_hero_dim_list = [self.single_hero_feature_dim, 512, 256, 128]
        self.hero_mlp = MLP(fc_hero_dim_list[:-1], "hero_mlp", non_linearity_last=True)
        self.hero_frd_fc = nn.Sequential(
            OrderedDict(
                [
                    (
                        "hero_frd_fc",
                        make_fc_layer(fc_hero_dim_list[-2], fc_hero_dim_list[-1]),
                    )
                ]
            )
        )
        self.hero_emy_fc = nn.Sequential(
            OrderedDict(
                [
                    (
                        "hero_emy_fc",
                        make_fc_layer(fc_hero_dim_list[-2], fc_hero_dim_list[-1]),
                    )
                ]
            )
        )
        
        fc_soldier_dim_list = [self.single_soldier_feature_dim, 64, 64, 32]
        self.soldier_mlp = MLP(fc_soldier_dim_list[:-1], "soldier_mlp", non_linearity_last=True)
        self.soldier_frd_fc = nn.Sequential(
            OrderedDict(
                [
                    (
                        "soldier_frd_fc",
                        make_fc_layer(fc_soldier_dim_list[-2], fc_soldier_dim_list[-1]),
                    )
                ]
            )
        )
        self.soldier_emy_fc = nn.Sequential(
            OrderedDict(
                [
                    (
                        "soldier_emy_fc",
                        make_fc_layer(fc_soldier_dim_list[-2], fc_soldier_dim_list[-1]),
                    )
                ]
            )
        )
        fc_organ_dim_list = [self.single_organ_feature_dim, 64, 64, 32]
        self.organ_mlp = MLP(fc_organ_dim_list[:-1], "organ_mlp", non_linearity_last=True)
        self.organ_frd_fc = nn.Sequential(
            OrderedDict(
                [
                    (
                        "organ_frd_fc",
                        make_fc_layer(fc_organ_dim_list[-2], fc_organ_dim_list[-1]),
                    )
                ]
            )
        )
        self.organ_emy_fc = nn.Sequential(
            OrderedDict(
                [
                    (
                        "organ_emy_fc",
                        make_fc_layer(fc_organ_dim_list[-2], fc_organ_dim_list[-1]),
                    )
                ]
            )
        )

        
        concat_dim = (
            fc_hero_main_dim_list[-1]
            + 2 * fc_hero_dim_list[-1]
            + 2 * fc_soldier_dim_list[-1]
            + 2 * fc_organ_dim_list[-1]
            + self.global_feature_dim
        )
        fc_concat_dim_list = [concat_dim, 512]
        self.concat_mlp = MLP(fc_concat_dim_list, "concat_mlp", non_linearity_last=True)

        # LSTM
        self.lstm = nn.LSTM(
            input_size=self.lstm_unit_size,
            hidden_size=self.lstm_unit_size,
            num_layers=1,
            bias=True,
            batch_first=True,
            dropout=0.0,
            bidirectional=False,
        )

        """output label"""
        self.label_mlp = ModuleDict(
            {
                "hero_label{0}_mlp".format(label_index): MLP(
                    [self.lstm_unit_size, 128, self.label_size_list[label_index]],
                    "hero_label{0}_mlp".format(label_index),
                )
                for label_index in range(len(self.label_size_list) - 1)
            }
        )
        self.lstm_tar_embed_mlp = make_fc_layer(self.lstm_unit_size, self.target_embed_dim)

        """output value - 多头reward"""
        # 4个多头value，移除原始单头
        self.farm_value_mlp = MLP([self.lstm_unit_size, 128, 64, 1], "farm_value_mlp")
        self.kda_value_mlp = MLP([self.lstm_unit_size, 128, 64, 1], "kda_value_mlp")
        self.hp_value_mlp = MLP([self.lstm_unit_size, 128, 64, 1], "hp_value_mlp")
        self.tower_value_mlp = MLP([self.lstm_unit_size, 128, 64, 1], "tower_value_mlp")

        self.target_embed_mlp = make_fc_layer(32, self.target_embed_dim, use_bias=False)

    def forward(self, data_list, inference=False):
        # data_list: [feature_vec, lstm_hidden_init, lstm_cell_init]
        feature_vec, lstm_hidden_init, lstm_cell_init = data_list

        result_list = []
        feature_vec_split_list = feature_vec.split(
            [
                self.all_hero_feature_dim,
                self.all_soldier_feature_dim,
                self.all_organ_feature_dim,
                self.global_feature_dim,
            ],
            dim=1,
        )

        hero_vec_list = feature_vec_split_list[0].split(
            [
                int(np.sum(DimConfig.DIM_OF_HERO_FRD)),
                int(np.sum(DimConfig.DIM_OF_HERO_EMY)),
                int(np.sum(DimConfig.DIM_OF_HERO_MAIN)),
            ],
            dim=1,
        )
        soldier_vec_list = feature_vec_split_list[1].split(
            [
                int(np.sum(DimConfig.DIM_OF_SOLDIER_1_10)),
                int(np.sum(DimConfig.DIM_OF_SOLDIER_11_20)),
            ],
            dim=1,
        )
        organ_vec_list = feature_vec_split_list[2].split(
            [
                int(np.sum(DimConfig.DIM_OF_ORGAN_1_2)),
                int(np.sum(DimConfig.DIM_OF_ORGAN_3_4)),
            ],
            dim=1,
        )
        global_info_list = feature_vec_split_list[3]

        _soldier_1_10 = soldier_vec_list[0].split(DimConfig.DIM_OF_SOLDIER_1_10, dim=1)
        _soldier_11_20 = soldier_vec_list[1].split(DimConfig.DIM_OF_SOLDIER_11_20, dim=1)
        _organ_1_2 = organ_vec_list[0].split(DimConfig.DIM_OF_ORGAN_1_2, dim=1)
        _organ_3_4 = organ_vec_list[1].split(DimConfig.DIM_OF_ORGAN_3_4, dim=1)
        _hero_frd = hero_vec_list[0].split(DimConfig.DIM_OF_HERO_FRD, dim=1)
        _hero_emy = hero_vec_list[1].split(DimConfig.DIM_OF_HERO_EMY, dim=1)
        _hero_main = hero_vec_list[2].split(DimConfig.DIM_OF_HERO_MAIN, dim=1)
        _global_info = global_info_list
        tar_embed_list = []

        """ real computations"""
        # hero_main
        # 英雄主向量
        for index in range(len(_hero_main)):
            main_hero = self.hero_main_mlp(_hero_main[index])
        hero_main_result = main_hero
        
        hero_emy_result_list = []
        for index in range(len(_hero_emy)):
            hero_emy_mlp_out = self.hero_mlp(_hero_emy[index])
            hero_emy_fc_out = self.hero_emy_fc(hero_emy_mlp_out)
            _, split_1 = hero_emy_fc_out.split([96, 32], dim=1)
            tar_embed_list.append(split_1)
            hero_emy_result_list.append(hero_emy_fc_out)

        hero_emy_concat_result = torch.cat(hero_emy_result_list, dim=1)
        reshape_hero_emy = hero_emy_concat_result.reshape(-1, 1, 1, 128)
        pool_hero_emy, _ = reshape_hero_emy.max(dim=2)
        output_dim = int(np.prod(pool_hero_emy.shape[1:]))
        reshape_pool_hero_emy = pool_hero_emy.reshape(-1, output_dim)


        # hero_share
        # 英雄共享向量
        hero_frd_result_list = []
        for index in range(len(_hero_frd)):
            hero_frd_mlp_out = self.hero_mlp(_hero_frd[index])
            hero_frd_fc_out = self.hero_frd_fc(hero_frd_mlp_out)
            _, split_1 = hero_frd_fc_out.split([96, 32], dim=1)
            tar_embed_list.append(split_1)
            hero_frd_result_list.append(hero_frd_fc_out)

        hero_frd_concat_result = torch.cat(hero_frd_result_list, dim=1)
        reshape_hero_frd = hero_frd_concat_result.reshape(-1, 1, 1, 128)
        pool_hero_frd, _ = reshape_hero_frd.max(dim=2)
        output_dim = int(np.prod(pool_hero_frd.shape[1:]))
        reshape_pool_hero_frd = pool_hero_frd.reshape(-1, output_dim)

        # soldier_share
        # 士兵共享向量
        soldier_frd_result_list = []
        for index in range(len(_soldier_1_10)):
            soldier_frd_mlp_out = self.soldier_mlp(_soldier_1_10[index])
            soldier_frd_fc_out = self.soldier_frd_fc(soldier_frd_mlp_out)
            soldier_frd_result_list.append(soldier_frd_fc_out)

        soldier_frd_concat_result = torch.cat(soldier_frd_result_list, dim=1)
        reshape_soldier_frd = soldier_frd_concat_result.reshape(-1, 1, 4, 32)
        pool_soldier_frd, _ = reshape_soldier_frd.max(dim=2)
        output_dim = int(np.prod(pool_soldier_frd.shape[1:]))
        reshape_pool_frd_soldier = pool_soldier_frd.reshape(-1, output_dim)

        # soldier_emy_share
        # 士兵共享向量
        soldier_emy_result_list = []
        for index in range(len(_soldier_11_20)):
            soldier_emy_mlp_out = self.soldier_mlp(_soldier_11_20[index])
            soldier_emy_fc_out = self.soldier_emy_fc(soldier_emy_mlp_out)
            soldier_emy_result_list.append(soldier_emy_fc_out)
            tar_embed_list.append(soldier_emy_fc_out)

        soldier_emy_concat_result = torch.cat(soldier_emy_result_list, dim=1)
        reshape_soldier_emy = soldier_emy_concat_result.reshape(-1, 1, 4, 32)
        pool_soldier_emy, _ = reshape_soldier_emy.max(dim=2)
        output_dim = int(np.prod(pool_soldier_emy.shape[1:]))
        reshape_pool_emy_soldier = pool_soldier_emy.reshape(-1, output_dim)

        # organ_share
        # 建筑共享向量
        organ_frd_result_list = []
        for index in range(len(_organ_1_2)):
            organ_frd_mlp_out = self.organ_mlp(_organ_1_2[index])
            organ_frd_fc_out = self.organ_frd_fc(organ_frd_mlp_out)
            organ_frd_result_list.append(organ_frd_fc_out)

        organ_1_concat_result = torch.cat(organ_frd_result_list, dim=1)
        reshape_frd_organ = organ_1_concat_result.reshape(-1, 1, 1, 32)
        pool_frd_organ, _ = reshape_frd_organ.max(dim=2)
        output_dim = int(np.prod(pool_frd_organ.shape[1:]))
        reshape_pool_frd_organ = pool_frd_organ.reshape(-1, output_dim)

        # organ_emy_share
        # 建筑共享向量
        organ_emy_result_list = []
        for index in range(len(_organ_3_4)):
            organ_emy_mlp_out = self.organ_mlp(_organ_3_4[index])
            organ_emy_fc_out = self.organ_emy_fc(organ_emy_mlp_out)
            organ_emy_result_list.append(organ_emy_fc_out)

        organ_emy_concat_result = torch.cat(organ_emy_result_list, dim=1)
        reshape_emy_organ = organ_emy_concat_result.reshape(-1, 1, 1, 32)
        pool_emy_organ, _ = reshape_emy_organ.max(dim=2)
        output_dim = int(np.prod(pool_emy_organ.shape[1:]))
        reshape_pool_emy_organ = pool_emy_organ.reshape(-1, output_dim)
        tar_embed_list.append(reshape_pool_emy_organ)



        tar_embed_0 = 0.1 * torch.ones_like(tar_embed_list[-1]).to(feature_vec.device)
        tar_embed_list.insert(0, tar_embed_0)

        tar_embed_8 = 0.1 * torch.ones_like(tar_embed_list[-1]).to(feature_vec.device)
        tar_embed_list.append(tar_embed_8)

        concat_result = torch.cat(
            [
                reshape_pool_frd_soldier,
                reshape_pool_emy_soldier,
                reshape_pool_frd_organ,
                reshape_pool_emy_organ,
                hero_main_result,
                reshape_pool_hero_frd,
                reshape_pool_hero_emy,
                _global_info,
            ],
            dim=1,
        )

        fc_public_result = self.concat_mlp(concat_result)
        reshape_fc_public_result = fc_public_result.reshape(-1, self.lstm_time_steps, 512)

        # LSTM
        lstm_initial_state_in = [
            lstm_hidden_init.unsqueeze(0),
            lstm_cell_init.unsqueeze(0),
        ]
        lstm_outputs, state = self.lstm(reshape_fc_public_result, lstm_initial_state_in)
        lstm_outputs = torch.cat([lstm_outputs[:, idx, :] for idx in range(lstm_outputs.size(1))], dim=1)
        self.lstm_cell_output = state[1]
        self.lstm_hidden_output = state[0]

        reshape_lstm_outputs_result = lstm_outputs.reshape(-1, self.lstm_unit_size)

        # output label
        # 输出标签
        for label_index, label_dim in enumerate(self.label_size_list[:-1]):
            label_mlp_out = self.label_mlp["hero_label{0}_mlp".format(label_index)](reshape_lstm_outputs_result)
            result_list.append(label_mlp_out)

        lstm_tar_embed_result = self.lstm_tar_embed_mlp(reshape_lstm_outputs_result)

        tar_embedding = torch.stack(tar_embed_list, dim=1)

        ulti_tar_embedding = self.target_embed_mlp(tar_embedding)
        reshape_label_result = lstm_tar_embed_result.reshape(-1, self.target_embed_dim, 1)

        label_result = torch.matmul(ulti_tar_embedding, reshape_label_result)
        target_output_dim = int(np.prod(label_result.shape[1:]))

        reshape_label_result = label_result.reshape(-1, target_output_dim)
        result_list.append(reshape_label_result)

        # output value - 只有多头reward
        farm_value_result = self.farm_value_mlp(reshape_lstm_outputs_result)
        result_list.append(farm_value_result)

        kda_value_result = self.kda_value_mlp(reshape_lstm_outputs_result)
        result_list.append(kda_value_result)

        hp_value_result = self.hp_value_mlp(reshape_lstm_outputs_result)
        result_list.append(hp_value_result)

        tower_value_result = self.tower_value_mlp(reshape_lstm_outputs_result)
        result_list.append(tower_value_result)

        # prepare for infer graph
        # 准备推理图 - 只有4个多头value
        logits = torch.flatten(torch.cat(result_list[:-4], 1), start_dim=1)  # 排除4个value头
        farm_value = result_list[-4]
        kda_value = result_list[-3]
        hp_value = result_list[-2]
        tower_value = result_list[-1]

        if inference:
            # 在inference时组合多头value为单一value
            combined_value = farm_value + kda_value + hp_value + tower_value
            return [logits, combined_value, self.lstm_cell_output, self.lstm_hidden_output]
        else:
            return result_list


    def compute_loss(self, data_list, rst_list):
        seri_vec = data_list[0].reshape(-1, self.data_split_shape[0])
        usq_advantage = data_list[1].reshape(-1, self.data_split_shape[1])
        # 多头reward数据
        usq_farm_reward = data_list[2].reshape(-1, self.data_split_shape[2])
        usq_kda_reward = data_list[3].reshape(-1, self.data_split_shape[3])
        usq_hp_reward = data_list[4].reshape(-1, self.data_split_shape[4])
        usq_tower_reward = data_list[5].reshape(-1, self.data_split_shape[5])
        usq_is_train = data_list[-3].reshape(-1, self.data_split_shape[-3])

        # 调整索引，移除reward_sum后，从索引6开始
        usq_label_list = data_list[6: 6 + len(self.label_size_list)]
        for shape_index in range(len(self.label_size_list)):
            usq_label_list[shape_index] = (
                usq_label_list[shape_index].reshape(-1, self.data_split_shape[6 + shape_index]).long()
            )

        old_label_probability_list = data_list[6 + len(self.label_size_list): 6 + 2 * len(self.label_size_list)]
        for shape_index in range(len(self.label_size_list)):
            old_label_probability_list[shape_index] = old_label_probability_list[shape_index].reshape(
                -1, self.data_split_shape[6 + len(self.label_size_list) + shape_index]
            )

        usq_weight_list = data_list[6 + 2 * len(self.label_size_list): 6 + 3 * len(self.label_size_list)]
        for shape_index in range(len(self.label_size_list)):
            usq_weight_list[shape_index] = usq_weight_list[shape_index].reshape(
                -1, self.data_split_shape[6 + 2 * len(self.label_size_list) + shape_index],
            )

        # squeeze tensor
        advantage = usq_advantage.squeeze(dim=1)
        # 多头reward数据
        farm_reward = usq_farm_reward.squeeze(dim=1)
        kda_reward = usq_kda_reward.squeeze(dim=1)
        hp_reward = usq_hp_reward.squeeze(dim=1)
        tower_reward = usq_tower_reward.squeeze(dim=1)
        label_list = [ele.squeeze(dim=1) for ele in usq_label_list]
        weight_list = [weight.squeeze(dim=1) for weight in usq_weight_list]
        frame_is_train = usq_is_train.squeeze(dim=1)

        # 修改为支持多头value - 只有4个value头
        label_result = rst_list[:-4]  # 每个分支的 logits，排除4个value头
        farm_value_result = rst_list[-4]
        kda_value_result = rst_list[-3]
        hp_value_result = rst_list[-2]
        tower_value_result = rst_list[-1]

        # 解析合法动作掩码
        _, split_feature_legal_action = torch.split(
            seri_vec,
            [
                int(np.prod(self.seri_vec_split_shape[0])),
                int(np.prod(self.seri_vec_split_shape[1])),
            ],
            dim=1,
        )
        feature_legal_action_shape = list(self.seri_vec_split_shape[1])
        feature_legal_action_shape.insert(0, -1)
        feature_legal_action = split_feature_legal_action.reshape(feature_legal_action_shape)  # [B, SumA]
        legal_action_flag_list = torch.split(feature_legal_action, self.label_size_list, dim=1)

        # value 损失 - 只有多头reward
        farm_value_squeezed = farm_value_result.squeeze(dim=1)
        kda_value_squeezed = kda_value_result.squeeze(dim=1)
        hp_value_squeezed = hp_value_result.squeeze(dim=1)
        tower_value_squeezed = tower_value_result.squeeze(dim=1)

        # 多头value损失（使用对应的分头reward）
        self.farm_value_cost = 0.5 * torch.mean(torch.square(farm_reward - farm_value_squeezed), dim=0)
        self.kda_value_cost = 0.5 * torch.mean(torch.square(kda_reward - kda_value_squeezed), dim=0)
        self.hp_value_cost = 0.5 * torch.mean(torch.square(hp_reward - hp_value_squeezed), dim=0)
        self.tower_value_cost = 0.5 * torch.mean(torch.square(tower_reward - tower_value_squeezed), dim=0)

        # PPO policy & entropy（使用标准 masked_softmax，不给非法动作分配 min_policy）
        epsilon = 1e-12
        self.policy_cost = torch.tensor(0.0, device=seri_vec.device)
        self.entropy_cost = torch.tensor(0.0, device=seri_vec.device)
        self.entropy_cost_list = []

        # 归一化优势（可选，通常更稳定）
        adv = advantage
        adv = (adv - adv.mean()) / (adv.std().clamp_min(1e-6))

        prob_idx = 0  # old prob 的下标（只为强化分支计数）
        for task_index in range(len(self.is_reinforce_task_list)):
            logits = label_result[task_index]                   # [B, A_i]
            legal = legal_action_flag_list[task_index].float()  # [B, A_i]
            weights = weight_list[task_index].float()           # [B]

            if self.is_reinforce_task_list[task_index]:
                # 当前分支的 old prob
                old_prob = old_label_probability_list[task_index]  # [B, A_i]

                # 当前分支概率（masked softmax）
                new_prob = masked_softmax(logits, legal, dim=1, eps=epsilon)  # [B, A_i]

                # one-hot 选中动作
                actions_onehot = F.one_hot(label_list[task_index].long(), self.label_size_list[task_index]).float()
                new_policy_p = (actions_onehot * new_prob).sum(dim=1).clamp_min(epsilon)  # [B]
                old_policy_p = (actions_onehot * old_prob).sum(dim=1).clamp_min(epsilon)  # [B]

                ratio = new_policy_p / old_policy_p  # [B]

                # PPO-clip
                surr1 = ratio * adv
                surr2 = ratio.clamp(1.0 - self.clip_param, 1.0 + self.clip_param) * adv
                policy_loss_vec = -torch.minimum(surr1, surr2)  # [B]

                # 带权平均
                denom = torch.maximum(weights.sum(), torch.tensor(1.0, device=seri_vec.device))
                temp_policy_loss = (policy_loss_vec * weights).sum() / denom
                self.policy_cost = self.policy_cost + temp_policy_loss

                # 熵（masked）
                entropy = -(new_prob * safe_log(new_prob, epsilon)).sum(dim=1)  # [B]
                temp_entropy = - (entropy * weights).sum() / denom  # 注意：取负号作为“损失项”一致性
                self.entropy_cost = self.entropy_cost + temp_entropy
                self.entropy_cost_list.append(temp_entropy)
                prob_idx += 1
            else:
                # 非强化分支：不计算策略损失与熵（保持接口一致）
                self.entropy_cost_list.append(torch.tensor(0.0, device=seri_vec.device))

        # 总损失 - 只包含多头value损失
        self.multi_head_value_cost = (self.farm_value_cost + self.kda_value_cost +
                                     self.hp_value_cost + self.tower_value_cost)
        self.loss = (self.multi_head_value_cost + self.policy_cost + self.var_beta * self.entropy_cost)

        return self.loss, [
            self.loss,
            [self.policy_cost, self.entropy_cost,
             self.farm_value_cost, self.kda_value_cost, self.hp_value_cost, self.tower_value_cost],
        ]

    def set_train_mode(self):
        self.lstm_time_steps = Config.LSTM_TIME_STEPS
        self.train()

    def set_eval_mode(self):
        self.lstm_time_steps = 1
        self.eval()