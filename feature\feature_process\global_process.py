#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2024 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""

import math
from .feature_normalizer import FeatureNormalizer


class GlobalProcess:
    def __init__(self, camp):
        self.main_camp = camp
        self.num_time_segments = 5
        self.segment_duration_seconds = 120  # 10 minutes / 5 segments = 2 minutes/segment
        self.monster_feature_num = 11
        self.normalizer = FeatureNormalizer()
        self.transform_camp2_to_camp1 = camp == "PLAYERCAMP_2"

    def process(self, frame_state):
        """
        Processes global features.
        """
        global_features = []

        # Get game time one-hot encoding
        game_time_one_hot = self._get_game_time_one_hot(frame_state)
        global_features.extend(game_time_one_hot)

        # Process death events
        death_event_features = self.process_death_events(frame_state)
        #global_features.extend(death_event_features)

        # Process cake features relative to the main hero
        cake_features = self.process_cake_features(frame_state)
        global_features.extend(cake_features)

        # Process monster feature
        monster_feature = self.process_monster_feature(frame_state)
        global_features.extend(monster_feature)

        return global_features

    def _get_game_time_one_hot(self, frame_state):
        """
        Encodes the game time into a one-hot vector based on segments.
        The first 10 minutes are split into 5 segments of 2 minutes each.
        Time beyond 10 minutes falls into the last segment.
        """
        one_hot_vector = [0.0] * self.num_time_segments

        # 1 frame is approximately 33 ms.
        frame_no = frame_state.get("frameNo", 0)
        game_time_seconds = frame_no * 33 / 1000

        # Calculate the segment index
        segment_index = int(game_time_seconds / self.segment_duration_seconds)

        # Clamp the index to the last segment if time exceeds 10 minutes
        if segment_index >= self.num_time_segments:
            segment_index = self.num_time_segments - 1

        one_hot_vector[segment_index] = 1.0

        return one_hot_vector

    def process_cake_features(self, frame_state):
        """
        Processes features for both allied and enemy healing cakes, relative to the main hero.
        Returns a 6-dimensional vector: 
        [ally_exists, ally_rel_x, ally_rel_z, enemy_exists, enemy_rel_x, enemy_rel_z].
        """
        main_hero_info = None
        for hero in frame_state.get("hero_states", []):
            if hero.get("actor_state", {}).get("camp") == self.main_camp:
                main_hero_info = hero
                break

        if not main_hero_info:
            return [0.0] * 6

        hero_loc = main_hero_info["actor_state"]["location"]

        camp1_cake = None
        camp2_cake = None
        for cake in frame_state.get("cakes", []):
            cake_loc = cake.get("collider", {}).get("location", {})
            if not cake_loc:
                continue
            
            cake_x = cake_loc.get("x", 0)
            if cake_x < 0:
                camp1_cake = cake
            elif cake_x > 0:
                camp2_cake = cake

        ally_cake = camp1_cake if self.main_camp == "PLAYERCAMP_1" else camp2_cake
        enemy_cake = camp2_cake if self.main_camp == "PLAYERCAMP_1" else camp1_cake

        def _calculate_single_cake_features(cake):
            if cake:
                cake_loc = cake.get("collider", {}).get("location", {})
                exists = 1.0

                relative_x = cake_loc.get("x", 0) - hero_loc.get("x", 0)
                if self.transform_camp2_to_camp1:
                    relative_x = -relative_x

                relative_z = cake_loc.get("z", 0) - hero_loc.get("z", 0)
                if self.transform_camp2_to_camp1:
                    relative_z = -relative_z

                norm_x = self.normalizer.min_max(relative_x, -60000, 60000)
                norm_z = self.normalizer.min_max(relative_z, -60000, 60000)
                #print(f"norm_x: {norm_x}, norm_z: {norm_z}")
                return [exists, norm_x, norm_z]
            else:
                return [0.0, 0.0, 0.0]

        frd_features = _calculate_single_cake_features(ally_cake)
        enemy_features = _calculate_single_cake_features(enemy_cake)
        #print(f"frd_features: {frd_features}")
        #print(f"enemy_features: {enemy_features}")
        return frd_features + enemy_features

    def process_monster_feature(self, frame_state):
        """处理野怪特征，作为全局信息"""
        monster_info = None
        main_hero_info = None
        enemy_camp_hero_dict = {}

        # 获取双方英雄信息
        for hero in frame_state.get("hero_states", []):
            if hero.get("actor_state", {}).get("camp") == self.main_camp:
                main_hero_info = hero
            else:
                enemy_camp_hero_dict[hero.get("actor_state", {}).get("config_id")] = hero

        for unit in frame_state.get("npc_states", []):
            if unit.get("sub_type") == "ACTOR_SUB_NONE" and unit.get("camp") == "PLAYERCAMP_MID":
                monster_info = unit
                break

        if monster_info and main_hero_info:
            features = []
            # is_alive
            features.append(1.0 if monster_info.get("hp", 0) > 0 else 0.0)
            # hp_rate
            max_hp = monster_info.get("max_hp", 0)
            features.append(monster_info.get("hp", 0) / max_hp if max_hp > 0 else 0.0)
            # max_hp
            features.append(self.normalizer.min_max(max_hp, 0, 4000))
            # location_x
            loc_x = monster_info.get("location", {}).get("x", 0)
            if self.transform_camp2_to_camp1 and loc_x != 100000:
                loc_x = -loc_x
            features.append(self.normalizer.min_max(loc_x, -10000.0, 10000.0))
            #print(f"loc_x: {loc_x}")
            # location_z
            loc_z = monster_info.get("location", {}).get("z", 0)
            if self.transform_camp2_to_camp1 and loc_z != 100000:
                loc_z = -loc_z
            features.append(self.normalizer.min_max(loc_z, -10000.0, 10000.0))
            #print(f"loc_z: {loc_z}")
            # relative_location_x
            hero_loc_x = main_hero_info.get("actor_state", {}).get("location", {}).get("x", 0)
            monster_loc_x = monster_info.get("location", {}).get("x", 0)
            x_diff = monster_loc_x - hero_loc_x
            if self.transform_camp2_to_camp1 and monster_loc_x != 100000:
                x_diff = -x_diff
            #print(f"x_diff: {x_diff}")
            features.append(self.normalizer.min_max(x_diff, 25000.0, 50000.0))
            # relative_location_z
            hero_loc_z = main_hero_info.get("actor_state", {}).get("location", {}).get("z", 0)
            monster_loc_z = monster_info.get("location", {}).get("z", 0)
            z_diff = monster_loc_z - hero_loc_z
            if self.transform_camp2_to_camp1 and monster_loc_z != 100000:
                z_diff = -z_diff
            #print(f"z_diff: {z_diff}")
            features.append(self.normalizer.min_max(z_diff, 25000.0, 50000.0))
            # attack_power
            atk = monster_info.get("values", {}).get("phy_atk", 0)
            features.append(self.normalizer.min_max(atk, 0, 140))
            # kill_income
            kill_income = monster_info.get("kill_income", 0)
            features.append(self.normalizer.min_max(kill_income, 0, 90))
            # 计算野怪与双方英雄的距离
            monster_location = monster_info.get("location", {})

            # Distance to main hero
            dist_to_main = 116000.0
            if main_hero_info:
                main_hero_location = main_hero_info.get("actor_state", {}).get("location", {})

                # 考慮陣營反轉：PLAYERCAMP_2的座標需要反轉
                if self.transform_camp2_to_camp1:
                    # 創建反轉後的座標
                    transformed_monster_location = {
                        "x": -monster_location.get("x", 0) if monster_location.get("x", 0) != 100000 else monster_location.get("x", 0),
                        "z": -monster_location.get("z", 0) if monster_location.get("z", 0) != 100000 else monster_location.get("z", 0)
                    }
                    transformed_main_hero_location = {
                        "x": -main_hero_location.get("x", 0) if main_hero_location.get("x", 0) != 100000 else main_hero_location.get("x", 0),
                        "z": -main_hero_location.get("z", 0) if main_hero_location.get("z", 0) != 100000 else main_hero_location.get("z", 0)
                    }
                    dist_to_main = self.cal_dist(transformed_monster_location, transformed_main_hero_location) * 100
                else:
                    dist_to_main = self.cal_dist(monster_location, main_hero_location) * 100
                #print(f"dist_to_main: {dist_to_main}")

            # Distance to enemy hero
            dist_to_enemy = 116000.0
            if enemy_camp_hero_dict:
                enemy_hero = next(iter(enemy_camp_hero_dict.values()))
                enemy_hero_location = enemy_hero.get("actor_state", {}).get("location", {})

                # 考慮陣營反轉：PLAYERCAMP_2的座標需要反轉
                if self.transform_camp2_to_camp1:
                    # 創建反轉後的座標
                    transformed_monster_location = {
                        "x": -monster_location.get("x", 0) if monster_location.get("x", 0) != 100000 else monster_location.get("x", 0),
                        "z": -monster_location.get("z", 0) if monster_location.get("z", 0) != 100000 else monster_location.get("z", 0)
                    }
                    transformed_enemy_hero_location = {
                        "x": -enemy_hero_location.get("x", 0) if enemy_hero_location.get("x", 0) != 100000 else enemy_hero_location.get("x", 0),
                        "z": -enemy_hero_location.get("z", 0) if enemy_hero_location.get("z", 0) != 100000 else enemy_hero_location.get("z", 0)
                    }
                    dist_to_enemy = self.cal_dist(transformed_monster_location, transformed_enemy_hero_location) * 100
                else:
                    dist_to_enemy = self.cal_dist(monster_location, enemy_hero_location) * 100
                #print(f"dist_to_enemy: {dist_to_enemy}")
            norm_dist_to_main = self.normalizer.min_max(dist_to_main, 30000, 70000.0)
            norm_dist_to_enemy = self.normalizer.min_max(dist_to_enemy, 120000, 160000.0)
            #print(f"norm_dist_to_main: {norm_dist_to_main}, norm_dist_to_enemy: {norm_dist_to_enemy}")
            features.extend([norm_dist_to_main, norm_dist_to_enemy])
            #print(f"features: {features}")
            return features
        else:
            return [0.0] * self.monster_feature_num
    
    def cal_dist(self, pos1, pos2):
        if not pos1 or not pos2:
            return 0
        dist = math.sqrt((pos1.get("x", 0) / 100.0 - pos2.get("x", 0) / 100.0) ** 2 + (pos1.get("z", 0) / 100.0 - pos2.get("z", 0) / 100.0) ** 2)
        return dist

    def process_death_events(self, frame_state):
        """處理死亡事件特徵 - 使用one hot編碼"""
        death_features = []
        
        # 獲取死亡事件列表
        frame_action = frame_state.get('frame_action', {})
        dead_actions = frame_action.get('dead_action', [])
        
        # 最多處理最近3個死亡事件，每個事件用one hot編碼
        max_death_events = 3
        
        # 為每個死亡事件創建one hot編碼
        # 編碼格式：[我方擊殺敵方英雄, 敵方擊殺我方英雄, 我方擊殺敵方小兵, 敵方擊殺我方小兵, 我方擊殺敵方塔, 敵方擊殺我方塔, 我方擊殺野怪, 敵方擊殺野怪]
        for i in range(max_death_events):
            if i < len(dead_actions):
                death_event = dead_actions[i]
                death = death_event.get('death', {})
                killer = death_event.get('killer', {})
                
                # 獲取死亡單位信息
                death_sub_type = death.get('sub_type', '')
                death_camp = death.get('camp', '')
                
                # 獲取擊殺者信息
                killer_camp = killer.get('camp', '')
                
                # 判斷死亡單位類型
                if death_sub_type == "ACTOR_SUB_HERO":
                    death_type = 'hero'
                elif death_sub_type == "ACTOR_SUB_SOLDIER":
                    death_type = 'soldier'
                elif death_sub_type == "ACTOR_SUB_TOWER":
                    death_type = 'tower'
                elif death_sub_type == "ACTOR_SUB_NONE":
                    death_type = 'monster'
                else:
                    death_type = 'other'
                
                # 判斷陣營關係
                is_our_camp = death_camp == self.main_camp
                is_enemy_camp = death_camp != self.main_camp and death_camp != ''
                is_our_killer = killer_camp == self.main_camp
                is_enemy_killer = killer_camp != self.main_camp and killer_camp != ''
                
                # 創建one hot編碼
                death_one_hot = [0, 0, 0, 0, 0, 0, 0, 0]  # 8個類別
                
                if death_type == 'hero':
                    if is_our_camp and is_enemy_killer:
                        death_one_hot[1] = 1  # 敵方擊殺我方英雄
                    elif is_enemy_camp and is_our_killer:
                        death_one_hot[0] = 1  # 我方擊殺敵方英雄
                elif death_type == 'soldier':
                    if is_our_camp and is_enemy_killer:
                        death_one_hot[3] = 1  # 敵方擊殺我方小兵
                    elif is_enemy_camp and is_our_killer:
                        death_one_hot[2] = 1  # 我方擊殺敵方小兵
                elif death_type == 'tower':
                    if is_our_camp and is_enemy_killer:
                        death_one_hot[5] = 1  # 敵方擊殺我方塔
                    elif is_enemy_camp and is_our_killer:
                        death_one_hot[4] = 1  # 我方擊殺敵方塔
                elif death_type == 'monster':
                    if is_our_killer:
                        death_one_hot[6] = 1  # 我方擊殺野怪
                    elif is_enemy_killer:
                        death_one_hot[7] = 1  # 敵方擊殺野怪
                
                death_features.extend(death_one_hot)
            else:
                # 如果沒有死亡事件，用0填充
                death_features.extend([0, 0, 0, 0, 0, 0, 0, 0])
        
        return death_features




    def _get_opposite_camp(self):
        """获取对方阵营"""
        return "PLAYERCAMP_1" if self.main_camp == "PLAYERCAMP_2" else "PLAYERCAMP_2"

