#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2024 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""

from enum import Enum
from .feature_normalizer import FeatureNormalizer
import configparser
import os
import math
from collections import OrderedDict
import numpy as np

class HeroProcess:
    def __init__(self, camp):
        self.normalizer = FeatureNormalizer()
        self.main_camp = camp
        self.main_camp_hero_dict = {}
        self.enemy_camp_hero_dict = {}
        self.main_camp_organ_dict = {}
        self.enemy_camp_organ_dict = {}
        self.transform_camp2_to_camp1 = camp == "PLAYERCAMP_2"
        self.view_dist = 15000
        self.one_unit_feature_num = 81
        self.unit_buff_num = 1
        self.hero_config_map = {
            169: 0,
            173: 1,
            174: 2,
        }

    def get_hero_config(self):
        self.config = configparser.ConfigParser()
        self.config.optionxform = str
        current_dir = os.path.dirname(__file__)
        config_path = os.path.join(current_dir, "hero_feature_config.ini")
        self.config.read(config_path)

        # Get normalized configuration
        # 获取归一化的配置
        self.hero_feature_config = []
        for feature, config in self.config["feature_config"].items():
            self.hero_feature_config.append(f"{feature}:{config}")

        # Get feature function configuration
        # 获取特征函数的配置
        self.feature_func_map = {}
        for feature, func_name in self.config["feature_functions"].items():
            if hasattr(self, func_name):
                self.feature_func_map[feature] = getattr(self, func_name)
            else:
                raise ValueError(f"Unsupported function: {func_name}")

    def process_vec_hero(self, frame_state):
        self.generate_hero_info_list(frame_state)
        self.generate_organ_info_dict(frame_state)

        local_vector_feature = []

        # Generate hero features for our camp
        # 生成我方阵营的英雄特征
        main_camp_hero_vector_feature = self.generate_one_type_hero_feature(
            self.main_camp_hero_dict, frame_state
        )
        local_vector_feature.extend(main_camp_hero_vector_feature)

        # Generate features for enemy team's heroes
        # 生成敌方阵营的英雄特征
        enemy_camp_hero_vector_feature = self.generate_one_type_hero_feature(
            self.enemy_camp_hero_dict, frame_state
        )
        local_vector_feature.extend(enemy_camp_hero_vector_feature)

        return local_vector_feature

    def generate_organ_info_dict(self, frame_state):
        self.main_camp_organ_dict.clear()
        self.enemy_camp_organ_dict.clear()
        for organ in frame_state.get("npc_states", []):
            if organ.get("sub_type") == "ACTOR_SUB_TOWER":
                if organ["camp"] == self.main_camp:
                    self.main_camp_organ_dict["tower"] = organ
                else:
                    self.enemy_camp_organ_dict["tower"] = organ

    def generate_hero_info_list(self, frame_state):
        self.main_camp_hero_dict.clear()
        self.enemy_camp_hero_dict.clear()
        for hero in frame_state["hero_states"]:
            if hero["actor_state"]["camp"] == self.main_camp:
                self.main_camp_hero_dict[hero["actor_state"]["config_id"]] = hero
                self.main_hero_info = hero
            else:
                self.enemy_camp_hero_dict[hero["actor_state"]["config_id"]] = hero

    def generate_one_type_hero_feature(self, one_type_hero_info, frame_state):
        vector_feature = []
        num_heros_considered = 0
        for hero in one_type_hero_info.values():
            if num_heros_considered >= self.unit_buff_num:
                break

            # Generate each specific feature through hardcoded calls
            vector_feature.extend(self.is_alive(hero))
            vector_feature.extend(self.get_hp_rate(hero))
            vector_feature.extend(self.get_max_hp(hero))
            vector_feature.extend(self.get_location_x(hero))
            vector_feature.extend(self.get_location_z(hero))
            vector_feature.extend(self.get_relative_location_x(hero))
            vector_feature.extend(self.get_relative_location_z(hero))
            vector_feature.extend(self.get_forward(hero))
            vector_feature.extend(self.get_ep_rate(hero))
            vector_feature.extend(self.get_max_ep(hero))
            vector_feature.extend(self.get_hp_recover(hero))
            vector_feature.extend(self.get_ep_recover(hero))
            vector_feature.extend(self.get_phy_atk(hero))
            vector_feature.extend(self.get_mgc_atk(hero))
            vector_feature.extend(self.get_phy_def(hero))
            vector_feature.extend(self.get_mgc_def(hero))
            vector_feature.extend(self.get_level(hero))
            vector_feature.extend(self.get_kill_count(hero))
            vector_feature.extend(self.get_dead_count(hero))
            vector_feature.extend(self.get_money_count(hero))
            vector_feature.extend(self.get_mov_spd(hero))
            vector_feature.extend(self.get_attack_range(hero))
            vector_feature.extend(self.get_atk_spd(hero))
            vector_feature.extend(self.get_phy_armor_hurt(hero))
            vector_feature.extend(self.get_mgc_armor_hurt(hero))
            vector_feature.extend(self.get_cd_reduce(hero))
            vector_feature.extend(self.get_exp(hero))
            vector_feature.extend(self.get_money(hero))
            vector_feature.extend(self.get_revive_time(hero))
            vector_feature.extend(self.get_kill_income(hero))
            vector_feature.extend(self.get_skill_features(hero))
            vector_feature.extend(self.get_crit_rate(hero))
            vector_feature.extend(self.get_crit_effe(hero))
            vector_feature.extend(self.get_phy_vamp(hero))
            vector_feature.extend(self.get_mgc_vamp(hero))
            vector_feature.extend(self.get_ctrl_reduce(hero))
            vector_feature.extend(self.get_tower_related_features(hero))
            vector_feature.extend(self.get_is_in_grass(hero))
            vector_feature.extend(self.get_buff_skills(hero))
            vector_feature.extend(self.get_buff_marks_features(hero))
            vector_feature.extend(self.get_attack_target_feature(hero, frame_state))
            vector_feature.extend(self.get_hero_to_tower(hero, frame_state))
            vector_feature.extend(self.get_config_id_one_hot(hero))
            num_heros_considered += 1
            #if 'bullets' in frame_state:
            #frame_state['bullets'][i]['source_actor'] == frame_state['npc_states'][i]['runtime_id']
            #frame_state['bullets'][i]['slot_type'] 'SLOT_SKILL_{i}' i=0, 1,2,3 4維one hot
            #frame_state['bullets'][i]['location']['x'] frame_state['bullets'][i]['location']['z']
            

        if num_heros_considered < self.unit_buff_num:
            self.no_hero_feature(vector_feature, num_heros_considered)
        return vector_feature

    def no_hero_feature(self, vector_feature, num_heros_considered):
        for _ in range((self.unit_buff_num - num_heros_considered) * self.one_unit_feature_num):
            vector_feature.append(0)

    def is_alive(self, hero):
        value = 0.0
        if hero["actor_state"]["hp"] > 0:
            value = 1.0
        return [value]

    def get_hp_rate(self, hero):
        """英雄血量百分比"""
        actor_state = hero.get("actor_state", {})
        hp = actor_state.get("hp", 0)
        max_hp = actor_state.get("max_hp", 0)
        value = 0.0
        if max_hp > 0:
            value = hp / max_hp
        return [value]

    def get_max_hp(self, hero):
        """英雄最大血量"""
        actor_state = hero.get("actor_state", {})
        max_hp = actor_state.get("max_hp", 0)
        value = self.normalizer.min_max(max_hp, 0, 12000)
        return [value]

    def get_ep_rate(self, hero):
        """英雄當前能量值百分比"""
        actor_state = hero.get("actor_state", {})
        values = actor_state.get("values", {})
        ep = values.get("ep", 0)
        max_ep = values.get("max_ep", 0)
        value = 0.0
        if max_ep > 0:
            value = ep / max_ep
        return [value]

    def get_max_ep(self, hero):
        """英雄最大能量值"""
        actor_state = hero.get("actor_state", {})
        values = actor_state.get("values", {})
        max_ep = values.get("max_ep", 0)
        value = self.normalizer.min_max(max_ep, 0, 3000)
        return [value]

    def get_hp_recover(self, hero):
        """英雄生命回復速率"""
        actor_state = hero.get("actor_state", {})
        values = actor_state.get("values", {})
        hp_recover = values.get("hp_recover", 0)
        value = self.normalizer.min_max(hp_recover, 20, 220)
        return [value]

    def get_ep_recover(self, hero):
        """英雄能量回復速率"""
        actor_state = hero.get("actor_state", {})
        values = actor_state.get("values", {})
        ep_recover = values.get("ep_recover", 0)
        value = self.normalizer.min_max(ep_recover, -10, 70)
        return [value]

    def get_phy_atk(self, hero):
        """英雄物理攻擊力"""
        actor_state = hero.get("actor_state", {})
        values = actor_state.get("values", {})
        phy_atk = values.get("phy_atk", 0)
        value = self.normalizer.min_max(phy_atk, 100, 900)
        return [value]

    def get_mgc_atk(self, hero):
        """英雄魔法攻擊力"""
        actor_state = hero.get("actor_state", {})
        values = actor_state.get("values", {})
        mgc_atk = values.get("mgc_atk", 0)
        value = self.normalizer.min_max(mgc_atk, 0, 1000)
        return [value]

    def get_phy_def(self, hero):
        """英雄物理防禦力"""
        actor_state = hero.get("actor_state", {})
        values = actor_state.get("values", {})
        phy_def = values.get("phy_def", 0)
        value = self.normalizer.min_max(phy_def, 0, 1200)
        return [value]

    def get_mgc_def(self, hero):
        """英雄魔法防禦力"""
        actor_state = hero.get("actor_state", {})
        values = actor_state.get("values", {})
        mgc_def = values.get("mgc_def", 0)
        value = self.normalizer.min_max(mgc_def, 0, 700)
        return [value]

    def get_level(self, hero):
        """英雄等級 - 16維 one-hot"""
        level = hero.get("level", 1)
        one_hot = [0.0] * 16
        if 1 <= level <= 16:
            one_hot[level - 1] = 1.0
        return one_hot

    def get_kill_count(self, hero):
        """英雄擊殺數"""
        kill_cnt = hero.get("killCnt", 0)
        value = self.normalizer.min_max(kill_cnt, 0, 10)
        return [value]

    def get_dead_count(self, hero):
        """英雄死亡數"""
        dead_cnt = hero.get("deadCnt", 0)
        value = self.normalizer.min_max(dead_cnt, 0, 8)
        return [value]

    def get_money_count(self, hero):
        """英雄金錢數"""
        money_cnt = hero.get("moneyCnt", 0)
        value = self.normalizer.min_max(money_cnt, 0, 12000)
        return [value]
    
    

    def get_mov_spd(self, hero):
        """英雄移动速度"""
        mov_spd = hero.get("actor_state", {}).get("values", {}).get("mov_spd", 0)
        value = self.normalizer.min_max(mov_spd, 1250, 7000)
        #print(f"mov_spd: {mov_spd}, value: {value}")
        return [value]

    def get_attack_range(self, hero):
        """英雄攻击范围"""
        attack_range = hero.get("actor_state", {}).get("attack_range", 0)
        value = self.normalizer.min_max(attack_range, 2800, 8000)
        #print(f"attack_range: {attack_range}, value: {value}")
        return [value]

    def get_atk_spd(self, hero):
        """英雄攻击速度"""
        atk_spd = hero.get("actor_state", {}).get("values", {}).get("atk_spd", 0)
        value = self.normalizer.min_max(atk_spd, 0, 10000)
        #print(f"atk_spd: {atk_spd}, value: {value}")
        return [value]

    def get_phy_armor_hurt(self, hero):
        """英雄物理穿透"""
        phy_armor_hurt = hero.get("actor_state", {}).get("values", {}).get("phy_armor_hurt", 0)
        value = self.normalizer.min_max(phy_armor_hurt, 0, 4000)
        #print(f"phy_armor_hurt: {phy_armor_hurt}, value: {value}")
        return [value]

    def get_mgc_armor_hurt(self, hero):
        """英雄法术穿透"""
        mgc_armor_hurt = hero.get("actor_state", {}).get("values", {}).get("mgc_armor_hurt", 0)
        value = self.normalizer.min_max(mgc_armor_hurt, 0, 3400)
        #print(f"mgc_armor_hurt: {mgc_armor_hurt}, value: {value}")
        return [value]

    def get_cd_reduce(self, hero):
        """英雄冷却缩减"""
        cd_reduce = hero.get("actor_state", {}).get("values", {}).get("cd_reduce", 0)
        value = self.normalizer.min_max(cd_reduce, 0, 3500)
        #print(f"cd_reduce: {cd_reduce}, value: {value}")
        return [value]

    def get_exp(self, hero):
        """英雄当前经验值"""
        exp = hero.get("exp", 0)
        value = self.normalizer.min_max(exp, 0, 2202)
        #print(f"exp: {exp}, value: {value}")
        return [value]

    def get_money(self, hero):
        """英雄当前金钱"""
        money = hero.get("money", 0)
        value = self.normalizer.min_max(money, 0, 2000)
        #print(f"money: {money}, value: {value}")
        return [value]

    def get_revive_time(self, hero):
        """英雄复活时间"""
        revive_time = hero.get("revive_time", 0)
        value = self.normalizer.min_max(revive_time, 0, 46)
        #print(f"revive_time: {revive_time}, value: {value}")
        return [value]

    def get_kill_income(self, hero):
        """英雄击杀收益"""
        kill_income = hero.get("actor_state", {}).get("kill_income", 0)
        value = self.normalizer.min_max(kill_income, 20, 400)
        #print(f"kill_income: {kill_income}, value: {value}")
        return [value]

    def get_skill_features(self, hero):
        """英雄技能状态 (可用性和冷却时间)"""
        skill_features = []
        num_skills = 4
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])

        for i in range(num_skills):
            if i < len(slot_states):
                slot = slot_states[i]
                usable = 1.0 if slot.get("usable", False) else 0.0
                cooldown = slot.get("cooldown", 0)
                cooldown_max = slot.get("cooldown_max", 0)

                cooldown_rate = 0.0
                if cooldown_max > 0:
                    cooldown_rate = self.normalizer.min_max(cooldown, 0, cooldown_max)

                skill_features.append(usable)
                skill_features.append(cooldown_rate)
            else:
                skill_features.append(0.0)
                skill_features.append(0.0)

        return skill_features

    def get_crit_rate(self, hero):
        """英雄暴击率"""
        crit_rate = hero.get("actor_state", {}).get("values", {}).get("crit_rate", 0)
        value = self.normalizer.min_max(crit_rate, 0, 5000)
        return [value]

    def get_crit_effe(self, hero):
        """英雄暴击效果"""
        crit_effe = hero.get("actor_state", {}).get("values", {}).get("crit_effe", 0)
        value = self.normalizer.min_max(crit_effe, 9500, 17000)
        return [value]

    def get_phy_vamp(self, hero):
        """英雄物理吸血"""
        phy_vamp = hero.get("actor_state", {}).get("values", {}).get("phy_vamp", 0)
        value = self.normalizer.min_max(phy_vamp, 0, 4000)
        return [value]

    def get_mgc_vamp(self, hero):
        """英雄法术吸血"""
        mgc_vamp = hero.get("actor_state", {}).get("values", {}).get("mgc_vamp", 0)
        value = self.normalizer.min_max(mgc_vamp, 0, 3400)
        return [value]

    def get_ctrl_reduce(self, hero):
        """英雄韧性"""
        ctrl_reduce = hero.get("actor_state", {}).get("values", {}).get("ctrl_reduce", 0)
        value = self.normalizer.min_max(ctrl_reduce, 0, 3500)
        return [value]

    def get_tower_related_features(self, hero):
        """英雄与防御塔相关的特征"""
        hero_in_main_camp_tower_atk_range = 0.0
        hero_in_enemy_camp_tower_atk_range = 0.0
        is_hero_under_tower_atk = 0.0

        hero_loc = hero["actor_state"]["location"]
        hero_camp = hero["actor_state"]["camp"]
        hero_runtime_id = hero.get("actor_state", {}).get("runtime_id")

        main_tower = self.main_camp_organ_dict.get("tower")
        enemy_tower = self.enemy_camp_organ_dict.get("tower")

        # 1. Calculate range features (relative to fixed towers on the map)
        if main_tower and main_tower.get("hp", 0) > 0:
            dist_to_main_tower = self.cal_dist(hero_loc, main_tower["location"]) * 100
            #print(f"dist_to_main_tower: {dist_to_main_tower}")
            if dist_to_main_tower <= main_tower.get("attack_range", 0):
                hero_in_main_camp_tower_atk_range = 1.0

        if enemy_tower and enemy_tower.get("hp", 0) > 0:
            dist_to_enemy_tower = self.cal_dist(hero_loc, enemy_tower["location"]) * 100
            #print(f"dist_to_enemy_tower: {dist_to_enemy_tower}")
            if dist_to_enemy_tower <= enemy_tower.get("attack_range", 0):
                hero_in_enemy_camp_tower_atk_range = 1.0

        # 2. Check if hero is under attack by its opposing tower
        if hero_camp == self.main_camp:
            # Our hero can only be attacked by the enemy tower
            if enemy_tower and enemy_tower.get("hp", 0) > 0:
                if hero_runtime_id is not None and enemy_tower.get("attack_target") == hero_runtime_id:
                    is_hero_under_tower_atk = 1.0
        else:
            # Enemy hero can only be attacked by our main tower
            if main_tower and main_tower.get("hp", 0) > 0:
                if hero_runtime_id is not None and main_tower.get("attack_target") == hero_runtime_id:
                    is_hero_under_tower_atk = 1.0
        #print(f"hero_in_main_camp_tower_atk_range: {hero_in_main_camp_tower_atk_range}, hero_in_enemy_camp_tower_atk_range: {hero_in_enemy_camp_tower_atk_range}, is_hero_under_tower_atk: {is_hero_under_tower_atk}")
        return [
            hero_in_main_camp_tower_atk_range,
            hero_in_enemy_camp_tower_atk_range,
            is_hero_under_tower_atk,
        ]

    def get_config_id_one_hot(self, hero):
        """获取英雄 config_id 的 one-hot 编码"""
        one_hot_dim = len(self.hero_config_map)
        one_hot_vector = [0.0] * one_hot_dim
        config_id = hero.get("actor_state", {}).get("config_id")

        if config_id in self.hero_config_map:
            index = self.hero_config_map[config_id]
            one_hot_vector[index] = 1.0

        return one_hot_vector

    def get_is_in_grass(self, hero):
        """英雄是否在草丛中"""
        is_in_grass = hero.get("actor_state", {}).get("isInGrass", False)
        return [1.0 if is_in_grass else 0.0]

    def get_buff_skills(self, hero):
        """英雄身上的buff技能状态"""
        buff_vector = [0.0] * 3
        buff_skills = hero.get("buff_state", {}).get("buff_skills", [])
        for i in range(len(buff_vector)):
            if i < len(buff_skills) and buff_skills[i]:
                buff_vector[i] = 1.0
        #print(f"buff_vector: {buff_vector}")
        return buff_vector

    def get_buff_marks_features(self, hero):
        """英雄身上的buff marks状态 (最多3个)"""
        num_buff_slots = 3
        features_per_buff = 2  # [exists, normalized_layer]
        total_features = num_buff_slots * features_per_buff
        buff_features = [0.0] * total_features

        buff_marks = hero.get("buff_state", {}).get("buff_marks", [])

        for i in range(num_buff_slots):
            if i < len(buff_marks):
                buff = buff_marks[i]
                layers = buff.get("layer", 0)
                
                # 假设 layer 最大值为 10，这是一个可以调整的超参数
                normalized_layers = self.normalizer.min_max(layers, 0, 4)

                # 填充 "exists" 和 "normalized_layer"
                buff_features[i * features_per_buff] = 1.0
                buff_features[i * features_per_buff + 1] = normalized_layers
        #print(f"buff_features: {buff_features}")
        return buff_features

    def cal_dist(self, pos1, pos2):
        dist = math.sqrt((pos1["x"] / 100.0 - pos2["x"] / 100.0) ** 2 + (pos1["z"] / 100.0 - pos2["z"] / 100.0) ** 2)
        return dist

    def get_location_x(self, hero):
        value = hero["actor_state"]["location"]["x"]
        if self.transform_camp2_to_camp1 and value != 100000:
            value = 0 - value
        normalized_value = self.normalizer.min_max(value, -60000.0, 60000.0)
        #print(f"normalized_value: {normalized_value}")
        return [normalized_value]

    def get_location_z(self, hero):
        value = hero["actor_state"]["location"]["z"]
        if self.transform_camp2_to_camp1 and value != 100000:
            value = 0 - value
        normalized_value = self.normalizer.min_max(value, -60000.0, 60000.0)
        #print(f"normalized_value: {normalized_value}")
        return [normalized_value]

    def get_forward(self, hero):
        """英雄前向方向，使用min_max进行归一化"""
        forward_x = hero.get("actor_state", {}).get("forward", {}).get("x", 0)
        forward_z = hero.get("actor_state", {}).get("forward", {}).get("z", 0)

        # 处理阵营转换时的反转
        if self.transform_camp2_to_camp1:
            forward_x = -forward_x
            forward_z = -forward_z

        norm_x = self.normalizer.min_max(forward_x, -1000.0, 1000.0)
        norm_z = self.normalizer.min_max(forward_z, -1000.0, 1000.0)

        #print(f"norm_x: {norm_x}, norm_z: {norm_z}")
        return [norm_x, norm_z]

    def get_attack_target_feature(self, hero, frame_state):
        """获取英雄攻击目标类型 - 5维 one-hot"""
        attack_target_id = hero.get("actor_state", {}).get("attack_target", 0)
        one_hot = [0.0] * 5

        if attack_target_id == 0:
            one_hot[0] = 1.0  # No target
            return one_hot

        # Check if target is an enemy hero
        hero_camp = hero.get("actor_state", {}).get("camp")
        if hero_camp == self.main_camp:
            enemy_heroes = self.enemy_camp_hero_dict.values()
        else:
            enemy_heroes = self.main_camp_hero_dict.values()

        for enemy_hero in enemy_heroes:
            if enemy_hero.get("actor_state", {}).get("runtime_id") == attack_target_id:
                one_hot[1] = 1.0  # Targeting enemy hero
                return one_hot

        # Check if target is an NPC (soldier, tower, etc.)
        for npc in frame_state.get("npc_states", []):
            if npc.get("runtime_id") == attack_target_id:
                if npc.get("sub_type") == "ACTOR_SUB_SOLDIER":
                    one_hot[2] = 1.0  # Targeting enemy soldier
                    return one_hot
                elif npc.get("sub_type") == "ACTOR_SUB_TOWER":
                    one_hot[3] = 1.0  # Targeting enemy tower
                    return one_hot

        # If target not found in known categories
        one_hot[4] = 1.0  # Targeting other
        return one_hot

    def get_hero_to_tower(self, hero, frame_state):
        tower_location = None
        if hero["actor_state"]["camp"] == self.main_camp:
            for i in range(len(frame_state['npc_states'])):
                if frame_state['npc_states'][i]['config_id'] == 1112:
                    tower_location = frame_state['npc_states'][i]['location']
                    break
        else:
            for i in range(len(frame_state['npc_states'])):
                if frame_state['npc_states'][i]['config_id'] == 1111:
                    tower_location = frame_state['npc_states'][i]['location']
                    break

        if tower_location is None:
            return [0.0]
        dist = self.cal_dist(hero['actor_state']['location'], tower_location)

        norm_dist = self.normalize_distance(dist)
        return [norm_dist]

    

    def normalize_distance(self, dist: float) -> float:

        _K_DECAY_FIXED = 1.0 / (300 ** 2)

        return np.exp(-_K_DECAY_FIXED * (dist ** 2))

    def get_relative_location_x(self, hero):
        """英雄相對於主英雄的X軸位置"""
        if not hasattr(self, 'main_hero_info') or not self.main_hero_info:
            return [0.0]
        
        hero_location_x = hero["actor_state"]["location"]["x"]
        main_hero_location_x = self.main_hero_info["actor_state"]["location"]["x"]
        x_diff = hero_location_x - main_hero_location_x
        
        if self.transform_camp2_to_camp1 and hero_location_x != 100000:
            x_diff = -x_diff
        
        # 使用與參考代碼相同的歸一化方式：(x_diff + 15000) / 30000.0
        value = (x_diff + 15000) / 30000.0
        value = self.normalizer.min_max(value, 0, 1)
        return [value]

    def get_relative_location_z(self, hero):
        """英雄相對於主英雄的Z軸位置"""
        if not hasattr(self, 'main_hero_info') or not self.main_hero_info:
            return [0.0]
        
        hero_location_z = hero["actor_state"]["location"]["z"]
        main_hero_location_z = self.main_hero_info["actor_state"]["location"]["z"]
        z_diff = hero_location_z - main_hero_location_z
        
        if self.transform_camp2_to_camp1 and hero_location_z != 100000:
            z_diff = -z_diff
        
        # 使用與參考代碼相同的歸一化方式：(z_diff + 15000) / 30000.0
        value = (z_diff + 15000) / 30000.0
        value = self.normalizer.min_max(value, 0, 1)
        return [value]
