#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2024 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""

from enum import Enum
from .feature_normalizer import FeatureNormalizer
import configparser
import os
import math
from collections import OrderedDict
import numpy as np

class SoldierProcess:
    def __init__(self, camp):
        self.normalizer = FeatureNormalizer()
        self.main_camp = camp
        
        self.main_camp_hero_dict = {}
        self.enemy_camp_hero_dict = {}
        self.main_camp_soldiers = []
        self.enemy_camp_soldiers = []
        
        self.transform_camp2_to_camp1 = camp == "PLAYERCAMP_2"
        self.one_unit_feature_num = 15  # 每個小兵17個特徵
        self.top_k_soldiers = 4  # 選擇距離最近的4個小兵
        
    def cal_dist(self, pos1, pos2):
        dist = math.sqrt((pos1["x"] / 100.0 - pos2["x"] / 100.0) ** 2 + (pos1["z"] / 100.0 - pos2["z"] / 100.0) ** 2)
        return dist
        
    def get_soldier_config(self):
        self.config = configparser.ConfigParser()
        self.config.optionxform = str
        current_dir = os.path.dirname(__file__)
        config_path = os.path.join(current_dir, "soldier_feature_config.ini")
        self.config.read(config_path)
        
        # Get normalized configuration
        # 獲取歸一化的配置
        self.soldier_feature_config = []
        for feature, config in self.config["feature_config"].items():
            self.soldier_feature_config.append(f"{feature}:{config}")
            
        # Get feature function configuration
        # 獲取特徵函數的配置
        self.feature_func_map = {}
        for feature, func_name in self.config["feature_functions"].items():
            if hasattr(self, func_name):
                self.feature_func_map[feature] = getattr(self, func_name)
            else:
                raise ValueError(f"Unsupported function: {func_name}")
                
    def process_vec_soldier(self, frame_state):
        self.generate_hero_info_list(frame_state)
        self.generate_soldier_info_list(frame_state)
        
        local_vector_feature = []

        # 生成我方小兵特徵（按npc_states順序的前4個）
        main_soldier_feature = self.generate_top_k_soldier_feature(self.main_camp_soldiers, "main")
        local_vector_feature.extend(main_soldier_feature)
        
        # 生成敵方小兵特徵（按npc_states順序的前4個）
        enemy_soldier_feature = self.generate_top_k_soldier_feature(self.enemy_camp_soldiers, "enemy")
        local_vector_feature.extend(enemy_soldier_feature)
        
        
        #print(f"local_vector_feature: {local_vector_feature}")
        return local_vector_feature
        
    def generate_hero_info_list(self, frame_state):
        self.main_camp_hero_dict.clear()
        self.enemy_camp_hero_dict.clear()
        for hero in frame_state["hero_states"]:
            if hero["actor_state"]["camp"] == self.main_camp:
                self.main_camp_hero_dict[hero["actor_state"]["config_id"]] = hero
                self.main_hero_info = hero
            else:
                self.enemy_camp_hero_dict[hero["actor_state"]["config_id"]] = hero
                
    def generate_soldier_info_list(self, frame_state):
        self.main_camp_soldiers.clear()
        self.enemy_camp_soldiers.clear()
        
        # 從 npc_states 中篩選出所有小兵
        for unit in frame_state["npc_states"]:
            if unit["sub_type"] == "ACTOR_SUB_SOLDIER" and unit["hp"] > 0:
                if unit["camp"] == self.main_camp:
                    self.main_camp_soldiers.append(unit)
                else:
                    self.enemy_camp_soldiers.append(unit)

                    
    def cal_distance_to_hero(self, unit):
        """計算單位與我方英雄的距離"""
        hero_location = self.main_hero_info["actor_state"]["location"]
        unit_location = unit["location"]
        distance = self.cal_dist(hero_location, unit_location) * 100
        return distance
        
    def generate_top_k_soldier_feature(self, soldiers, camp_type):
        """生成按runtime_id升序排列的前K個小兵的特徵"""
        vector_feature = []

        if not soldiers:
            # 如果沒有小兵，用0填充所有位置
            self.no_soldier_feature(vector_feature, 0)
            return vector_feature

        # 按runtime_id升序排列，選擇前top_k_soldiers個小兵
        selected_soldiers = sorted(soldiers, key=lambda x: x.get("runtime_id", 0))[:self.top_k_soldiers]
        
        # 為每個選中的小兵生成特徵
        for soldier in selected_soldiers:
            vector_feature.extend(self.is_alive(soldier, vector_feature, "is_alive"))
            vector_feature.extend(self.get_hp_rate(soldier, vector_feature, "get_hp_rate"))
            vector_feature.extend(self.get_max_hp(soldier, vector_feature, "get_max_hp"))
            vector_feature.extend(self.get_location_x(soldier, vector_feature, "get_location_x"))
            vector_feature.extend(self.get_location_z(soldier, vector_feature, "get_location_z"))
            vector_feature.extend(self.relative_location_x(soldier, vector_feature, "relative_location_x"))
            vector_feature.extend(self.relative_location_z(soldier, vector_feature, "relative_location_z"))
            vector_feature.extend(self.get_camp(soldier, vector_feature, "get_camp"))
            vector_feature.extend(self.get_soldier_type(soldier, vector_feature, "get_soldier_type"))
            vector_feature.extend(self.get_attack_power(soldier, vector_feature, "get_attack_power"))
            vector_feature.extend(self.get_kill_income(soldier, vector_feature, "get_kill_income"))
            vector_feature.extend(self.get_dist_from_heroes(soldier, vector_feature, "get_dist_from_heroes"))
            #vector_feature.extend(self.is_in_hero_sight(soldier, vector_feature, "is_in_hero_sight"))
                        
        # 如果小兵數量不足top_k_soldiers，用0填充
        actual_count = len(selected_soldiers)
        if actual_count < self.top_k_soldiers:
            self.no_soldier_feature(vector_feature, actual_count)
            
        return vector_feature
        
    def no_soldier_feature(self, vector_feature, current_count):
        """用0填充缺失的小兵特徵位置"""
        missing_count = self.top_k_soldiers - current_count
        for _ in range(missing_count * self.one_unit_feature_num):
            vector_feature.append(0.0)
            
    def is_alive(self, soldier, vector_feature, feature_name):
        """小兵是否存活"""
        if soldier["hp"] > 0:
            return [1.0]
        else:
            return [0.0]
        
    def get_hp_rate(self, soldier, vector_feature, feature_name):
        """小兵血量比例"""
        value = 0.0
        if soldier["max_hp"] > 0:
            value = soldier["hp"] / soldier["max_hp"]
        return [value]

    def get_max_hp(self, soldier, vector_feature, feature_name):
        """小兵最大血量"""
        value = soldier["max_hp"]
        value = self.normalizer.min_max(value, 0, 12000)
        return [value]

    def get_location_x(self, soldier, vector_feature, feature_name):
        """小兵X座標"""
        value = soldier["location"]["x"]
        if self.transform_camp2_to_camp1 and value != 100000:
            value = 0 - value
        normalized_value = self.normalizer.min_max(value, -41000.0, 41000.0)
        #print(f"value: {value}, normalized_value: {normalized_value}")
        return [normalized_value]

    def get_location_z(self, soldier, vector_feature, feature_name):
        """小兵Z座標"""
        value = soldier["location"]["z"]
        if self.transform_camp2_to_camp1 and value != 100000:
            value = 0 - value
        normalized_value = self.normalizer.min_max(value, -41000.0, 41000.0)
        #print(f"value: {value}, normalized_value: {normalized_value}")
        return [normalized_value]
        
    def relative_location_x(self, soldier, vector_feature, feature_name):
        """相對於我方英雄的X座標"""
        soldier_x = soldier["location"]["x"]
        hero_x = self.main_hero_info["actor_state"]["location"]["x"]
        x_diff = soldier_x - hero_x
        
        if self.transform_camp2_to_camp1 and soldier_x != 100000:
            x_diff = -x_diff
            
        # 歸一化到[-1, 1]範圍
        value = (x_diff + 15000) / 30000.0
        
        #print(f"x_diff: {x_diff}, value: {value}")
        return [value]
        
    def relative_location_z(self, soldier, vector_feature, feature_name):
        """相對於我方英雄的Z座標"""
        soldier_z = soldier["location"]["z"]
        hero_z = self.main_hero_info["actor_state"]["location"]["z"]
        z_diff = soldier_z - hero_z
        
        if self.transform_camp2_to_camp1 and soldier_z != 100000:
            z_diff = -z_diff
            
        # 歸一化到[-1, 1]範圍
        value = (z_diff + 15000) / 30000.0
        #print(f"z_diff: {z_diff}, value: {value}")
        return [value]
        
    def get_camp(self, soldier, vector_feature, feature_name):
        """小兵陣營信息 - 2維 one hot: [1,0] 表示PLAYERCAMP_1, [0,1] 表示PLAYERCAMP_2"""
        if soldier["camp"] == self.main_camp:
            return [1.0, 0.0]
        else:
            return [0.0, 1.0]
        return [0.0, 0.0]

    def get_soldier_type(self, soldier, vector_feature, feature_name):
        """小兵類型信息 - 3維 one hot: [1,0,0] 近戰, [0,1,0] 遠程, [0,0,1] 炮車"""
        config_id = soldier.get("config_id", 0)
        if config_id in [6800, 6803]:  # Melee
            return [1.0, 0.0, 0.0]
        elif config_id in [6801, 6804]:  # Ranged
            return [0.0, 1.0, 0.0]
        elif config_id in [6802, 6805]:  # Siege
            return [0.0, 0.0, 1.0]
        return [0.0, 0.0, 0.0]

    def get_attack_power(self, soldier, vector_feature, feature_name):
        """小兵攻擊力"""
        value = soldier["values"]["phy_atk"]
        value = self.normalizer.min_max(value, 0, 700)
        return [value]
    
    def get_kill_income(self, soldier, vector_feature, feature_name):
        """小兵擊殺收入"""
        value = soldier["kill_income"]
        value = self.normalizer.min_max(value, 0, 150)
        return [value]

    def get_dist_from_heroes(self, soldier, vector_feature, feature_name):
        """計算小兵與雙方英雄的距離"""
        hero_location = []
        soldier_location = soldier["location"]
        #frd soldier -> emy hero
        if soldier['camp'] == self.main_camp:
            for hero_data in self.enemy_camp_hero_dict.values():
                location_x = hero_data['actor_state']['location']['x']
                location_x = 0 - location_x
                location_z = hero_data['actor_state']['location']['z']
                location_z = 0 - location_z
            soldier_location = soldier['location']
        #emy soldier
        else:
            for hero_data in self.main_camp_hero_dict.values():
                location_x = hero_data['actor_state']['location']['x']
                location_z = hero_data['actor_state']['location']['z']
            soldier['location']['x'] = 0 - soldier['location']['x']
            soldier['location']['z'] = 0 - soldier['location']['z']

        hero_location.append({'x': location_x, 'z': location_z})

        


        dist = self.cal_dist(soldier_location, hero_location[0]) 


        norm_dist = self.normalize_distance(dist)
        #print(norm_dist)
        return [norm_dist]


    def normalize_distance(self, dist: float) -> float:

        _K_DECAY_FIXED = 1.0 / (300 ** 2)

        return np.exp(-_K_DECAY_FIXED * (dist ** 2))

    def is_in_hero_sight(self, soldier, vector_feature, feature_name):
        """判斷小兵是否在我方英雄視野範圍 (7000) 內"""
        dist_to_main = 7001.0  # Default to outside sight
        if hasattr(self, 'main_hero_info'):
            soldier_location = soldier["location"]
            main_hero_location = self.main_hero_info["actor_state"]["location"]
            dist_to_main = self.cal_dist(soldier_location, main_hero_location) * 100

        if dist_to_main <= 7000.0:
            return [1.0]
        else:
            return [0.0]
