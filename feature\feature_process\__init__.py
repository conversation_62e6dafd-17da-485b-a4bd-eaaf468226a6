#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2024 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""

from agent_ppo.feature.feature_process.hero_process import HeroProcess
from agent_ppo.feature.feature_process.organ_process import OrganProcess
from agent_ppo.feature.feature_process.soldier_process import SoldierProcess
from agent_ppo.feature.feature_process.global_process import GlobalProcess
from agent_ppo.feature.feature_process.public_hero_main import PublicHeroMainProcess

class FeatureProcess:
    def __init__(self, camp):
        self.camp = camp
        self.hero_process = HeroProcess(camp)
        self.organ_process = OrganProcess(camp)
        self.soldier_process = SoldierProcess(camp)
        self.global_process = GlobalProcess(camp)
        self.public_hero_main_process = PublicHeroMainProcess(camp)

    def reset(self, camp):
        self.camp = camp
        self.hero_process = HeroProcess(camp)
        self.organ_process = OrganProcess(camp)
        self.soldier_process = SoldierProcess(camp)
        self.global_process = GlobalProcess(camp)
        self.public_hero_main_process = PublicHeroMainProcess(camp)

    def process_organ_feature(self, frame_state):
        return self.organ_process.process_vec_organ(frame_state)

    def process_hero_feature(self, frame_state):
        return self.hero_process.process_vec_hero(frame_state)

    def process_soldier_feature(self, frame_state):
        return self.soldier_process.process_vec_soldier(frame_state)

    def process_global_feature(self, frame_state):
        return self.global_process.process(frame_state)

    def process_public_hero_main_feature(self, frame_state):
        return self.public_hero_main_process.process_vec_public_hero_main(frame_state)

    def process_feature(self, observation):
        frame_state = observation["frame_state"]
        #藍方小兵
        #近戰
        #{'config_id': 6800, 'runtime_id': 38, 'actor_type': 'ACTOR_MONSTER', 'sub_type': 'ACTOR_SUB_SOLDIER', 'camp': 'PLAYERCAMP_1', 'behav_mode': 'Attack_Path', 'location': {'x': -25758, 'y': 48, 'z': -27667}, 'forward': {'x': -707, 'y': 0, 'z': 707}, 'hp': 1488, 'max_hp': 1488, 'values': {'phy_atk': 60, 'phy_def': 180, 'mgc_atk': 60, 'mgc_def': 0, 'mov_spd': 3088, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 750, ...}, 'abilities': [False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, ...], 'attack_range': 1500, 'attack_target': 0, 'kill_income': 75, 'camp_visible': [True, False], 'sight_area': 5500, 'buff_state': {}}
        #遠程
        #{'config_id': 6801, 'runtime_id': 41, 'actor_type': 'ACTOR_MONSTER', 'sub_type': 'ACTOR_SUB_SOLDIER', 'camp': 'PLAYERCAMP_1', 'behav_mode': 'Attack_Path', 'location': {'x': -25758, 'y': 48, 'z': -27667}, 'forward': {'x': -707, 'y': 0, 'z': 707}, 'hp': 1356, 'max_hp': 1356, 'values': {'phy_atk': 90, 'phy_def': 180, 'mgc_atk': 90, 'mgc_def': 0, 'mov_spd': 3088, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 750, ...}, 'abilities': [False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, ...], 'attack_range': 7000, 'attack_target': 0, 'kill_income': 46, 'camp_visible': [True, False], 'sight_area': 8500, 'buff_state': {}}
        #炮車
        #{'config_id': 6802, 'runtime_id': 939, 'actor_type': 'ACTOR_MONSTER', 'sub_type': 'ACTOR_SUB_SOLDIER', 'camp': 'PLAYERCAMP_1', 'behav_mode': 'Attack_Path', 'location': {'x': -25758, 'y': 48, 'z': -27667}, 'forward': {'x': -707, 'y': 0, 'z': 707}, 'hp': 3311, 'max_hp': 3311, 'values': {'phy_atk': 205, 'phy_def': 180, 'mgc_atk': 205, 'mgc_def': 0, 'mov_spd': 3500, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 750, ...}, 'abilities': [False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, ...], 'attack_range': 7000, 'attack_target': 0, 'kill_income': 126, 'camp_visible': [True, False], 'sight_area': 8500, 'buff_state': {}}
        #紅方小兵
        #近戰
        #{'config_id': 6803, 'runtime_id': 39, 'actor_type': 'ACTOR_MONSTER', 'sub_type': 'ACTOR_SUB_SOLDIER', 'camp': 'PLAYERCAMP_2', 'behav_mode': 'Attack_Path', 'location': {'x': 24487, 'y': 48, 'z': 27683}, 'forward': {'x': -707, 'y': 0, 'z': 707}, 'hp': 1488, 'max_hp': 1488, 'values': {'phy_atk': 60, 'phy_def': 180, 'mgc_atk': 60, 'mgc_def': 0, 'mov_spd': 3088, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 750, ...}, 'abilities': [False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, ...], 'attack_range': 1500, 'attack_target': 0, 'kill_income': 75, 'camp_visible': [False, True], 'sight_area': 5500, 'buff_state': {}}
        #遠程
        #{'config_id': 6804, 'runtime_id': 43, 'actor_type': 'ACTOR_MONSTER', 'sub_type': 'ACTOR_SUB_SOLDIER', 'camp': 'PLAYERCAMP_2', 'behav_mode': 'Attack_Path', 'location': {'x': 24487, 'y': 48, 'z': 27683}, 'forward': {'x': -707, 'y': 0, 'z': 707}, 'hp': 1356, 'max_hp': 1356, 'values': {'phy_atk': 90, 'phy_def': 180, 'mgc_atk': 90, 'mgc_def': 0, 'mov_spd': 3088, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 750, ...}, 'abilities': [False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, ...], 'attack_range': 7000, 'attack_target': 0, 'kill_income': 46, 'camp_visible': [False, True], 'sight_area': 8500, 'buff_state': {}}
        #炮車
        #{'config_id': 6805, 'runtime_id': 940, 'actor_type': 'ACTOR_MONSTER', 'sub_type': 'ACTOR_SUB_SOLDIER', 'camp': 'PLAYERCAMP_2', 'behav_mode': 'Attack_Path', 'location': {'x': 24487, 'y': 48, 'z': 27683}, 'forward': {'x': -707, 'y': 0, 'z': 707}, 'hp': 3311, 'max_hp': 3311, 'values': {'phy_atk': 205, 'phy_def': 180, 'mgc_atk': 205, 'mgc_def': 0, 'mov_spd': 3500, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 750, ...}, 'abilities': [False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, ...], 'attack_range': 7000, 'attack_target': 0, 'kill_income': 126, 'camp_visible': [False, True], 'sight_area': 8500, 'buff_state': {}}
        #野怪?
        #{'config_id': 6827, 'runtime_id': 39, 'actor_type': 'ACTOR_MONSTER', 'sub_type': 'ACTOR_SUB_NONE', 'camp': 'PLAYERCAMP_MID', 'behav_mode': 'State_Idle', 'location': {'x': -4643, 'y': 48, 'z': 6583}, 'forward': {'x': 1000, 'y': 0, 'z': -11}, 'hp': 4000, 'max_hp': 4000, 'values': {'phy_atk': 140, 'phy_def': 0, 'mgc_atk': 140, 'mgc_def': 0, 'mov_spd': 2500, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, ...}, 'abilities': [False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, ...], 'attack_range': 100, 'attack_target': 0, 'kill_income': 90, 'camp_visible': [True, True], 'sight_area': 5000, 'buff_state': {}}

        main_camp_hero_vector_feature = self.process_hero_feature(frame_state)
        organ_feature = self.process_organ_feature(frame_state)
        soldier_feature = self.process_soldier_feature(frame_state)
        global_feature = self.process_global_feature(frame_state)
        public_hero_main_feature = self.process_public_hero_main_feature(frame_state)

        # 按照 model.py 中 feature_vec_split 的順序拼接：hero -> soldier -> organ -> global
        # hero: 79*2(友方+敵方) + 13(hero_main) = 171
        # soldier: 17*4*2 = 136
        # organ: 12*2 = 24
        # global: 62
        # 總計：171 + 136 + 24 + 46 = 377
        feature = main_camp_hero_vector_feature + public_hero_main_feature + soldier_feature + organ_feature + global_feature 

        #frame_state['cakes']
        #frame_state['cakes'][i]['collider']
        #frame_state['cakes'][i]['collider']['location']
        #frame_state['cakes'][i]['collider']['location']['x']
        #frame_state['cakes'][i]['collider']['location']['z']
        return feature
